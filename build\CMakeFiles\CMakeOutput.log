The system is: Windows - 10.0.22631 - AMD64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler:  
Build flags: 
Id flags:  

The output was:
0
适用于 .NET Framework MSBuild 版本 17.9.8+b34f75857
生成启动时间为 2025/7/14 13:03:13。

节点 1 上的项目“C:\Users\<USER>\Downloads\软件训练营\课程配套\编译构建与CMake基础\01_part\class03\build\CMakeFiles\3.24.0-rc1\CompilerIdC\CompilerIdC.vcxproj”(默认目标)。
PrepareForBuild:
  正在创建目录“Debug\”。
  已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
  正在创建目录“Debug\CompilerIdC.tlog\”。
InitializeBuildStatus:
  正在创建“Debug\CompilerIdC.tlog\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
  正在对“Debug\CompilerIdC.tlog\unsuccessfulbuild”执行 Touch 任务。
ClCompile:
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.39.33519\bin\HostX64\x64\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\" /Fd"Debug\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
  CMakeCCompilerId.c
Link:
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.39.33519\bin\HostX64\x64\link.exe /ERRORREPORT:QUEUE /OUT:".\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\CompilerIdC.lib" /MACHINE:X64 Debug\CMakeCCompilerId.obj
  CompilerIdC.vcxproj -> C:\Users\<USER>\Downloads\软件训练营\课程配套\编译构建与CMake基础\01_part\class03\build\CMakeFiles\3.24.0-rc1\CompilerIdC\CompilerIdC.exe
PostBuildEvent:
  for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
  :VCEnd
  CMAKE_C_COMPILER=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64\cl.exe
FinalizeBuildStatus:
  正在删除文件“Debug\CompilerIdC.tlog\unsuccessfulbuild”。
  正在对“Debug\CompilerIdC.tlog\CompilerIdC.lastbuildstate”执行 Touch 任务。
已完成生成项目“C:\Users\<USER>\Downloads\软件训练营\课程配套\编译构建与CMake基础\01_part\class03\build\CMakeFiles\3.24.0-rc1\CompilerIdC\CompilerIdC.vcxproj”(默认目标)的操作。

已成功生成。
    0 个警告
    0 个错误

已用时间 00:00:00.37


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"

Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"

The C compiler identification is MSVC, found in "C:/Users/<USER>/Downloads/软件训练营/课程配套/编译构建与CMake基础/01_part/class03/build/CMakeFiles/3.24.0-rc1/CompilerIdC/CompilerIdC.exe"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler:  
Build flags: 
Id flags:  

The output was:
0
适用于 .NET Framework MSBuild 版本 17.9.8+b34f75857
生成启动时间为 2025/7/14 13:03:14。

节点 1 上的项目“C:\Users\<USER>\Downloads\软件训练营\课程配套\编译构建与CMake基础\01_part\class03\build\CMakeFiles\3.24.0-rc1\CompilerIdCXX\CompilerIdCXX.vcxproj”(默认目标)。
PrepareForBuild:
  正在创建目录“Debug\”。
  已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
  正在创建目录“Debug\CompilerIdCXX.tlog\”。
InitializeBuildStatus:
  正在创建“Debug\CompilerIdCXX.tlog\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
  正在对“Debug\CompilerIdCXX.tlog\unsuccessfulbuild”执行 Touch 任务。
ClCompile:
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.39.33519\bin\HostX64\x64\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\" /Fd"Debug\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
  CMakeCXXCompilerId.cpp
Link:
  C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.39.33519\bin\HostX64\x64\link.exe /ERRORREPORT:QUEUE /OUT:".\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\CompilerIdCXX.lib" /MACHINE:X64 Debug\CMakeCXXCompilerId.obj
  CompilerIdCXX.vcxproj -> C:\Users\<USER>\Downloads\软件训练营\课程配套\编译构建与CMake基础\01_part\class03\build\CMakeFiles\3.24.0-rc1\CompilerIdCXX\CompilerIdCXX.exe
PostBuildEvent:
  for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
  :VCEnd
  CMAKE_CXX_COMPILER=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64\cl.exe
FinalizeBuildStatus:
  正在删除文件“Debug\CompilerIdCXX.tlog\unsuccessfulbuild”。
  正在对“Debug\CompilerIdCXX.tlog\CompilerIdCXX.lastbuildstate”执行 Touch 任务。
已完成生成项目“C:\Users\<USER>\Downloads\软件训练营\课程配套\编译构建与CMake基础\01_part\class03\build\CMakeFiles\3.24.0-rc1\CompilerIdCXX\CompilerIdCXX.vcxproj”(默认目标)的操作。

已成功生成。
    0 个警告
    0 个错误

已用时间 00:00:00.37


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"

Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"

The CXX compiler identification is MSVC, found in "C:/Users/<USER>/Downloads/软件训练营/课程配套/编译构建与CMake基础/01_part/class03/build/CMakeFiles/3.24.0-rc1/CompilerIdCXX/CompilerIdCXX.exe"

Detecting C compiler ABI info compiled with the following output:
Change Dir: C:/Users/<USER>/Downloads/软件训练营/课程配套/编译构建与CMake基础/01_part/class03/build/CMakeFiles/CMakeTmp

Run Build Command(s):C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_fac40.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:m && 适用于 .NET Framework MSBuild 版本 17.9.8+b34f75857



  用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.39.33523 版

  版权所有(C) Microsoft Corporation。保留所有权利。

  cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\"Debug\"" /Gm- /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_fac40.dir\Debug\\" /Fd"cmTC_fac40.dir\Debug\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\Program Files\CMake\share\cmake-3.24\Modules\CMakeCCompilerABI.c"

  CMakeCCompilerABI.c

  cmTC_fac40.vcxproj -> C:\Users\<USER>\Downloads\软件训练营\课程配套\编译构建与CMake基础\01_part\class03\build\CMakeFiles\CMakeTmp\Debug\cmTC_fac40.exe




